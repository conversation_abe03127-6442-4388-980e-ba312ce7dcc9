import 'package:collection/collection.dart';
import 'package:ez_firebase/ez_firebase.dart';
import 'package:flutter/material.dart';

import '../../data/models/notification_model.dart';

enum NotificationDisplayType {
  checkInReminder,
  checkOutReminder,
  requestCreated,
  requestApproved,
  requestHandled,
  general;
}

class InAppNotificationWidget extends StatelessWidget {
  const InAppNotificationWidget({
    super.key,
    this.notification,
    required this.message,
    this.onAction,
  });
  final RemoteMessage message;
  final NotificationModel? notification;
  final VoidCallback? onAction;

  @override
  Widget build(final BuildContext context) {
    final type = NotificationDisplayType.values.firstWhereOrNull(
          (final e) => e.name == notification?.type,
        ) ??
        NotificationDisplayType.general;

    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Material(
          borderRadius: BorderRadius.circular(16),
          elevation: 6,
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message.notification?.title ?? 'Title',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  // notification?.content ?? 'Nội dung thông báo',
                  message.notification?.body ?? 'Body',
                  style: const TextStyle(fontSize: 14),
                ),
                if (_hasSiteInfo(notification)) ...[
                  const SizedBox(height: 16),
                  const Row(
                    children: [
                      Icon(Icons.location_on_outlined, size: 28),
                      SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Worksite',
                              // notification?.siteName ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'Address',
                              // notification?.siteAddress ?? 'Địa chỉ',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 16),
                if (_hasActionButton(type)) ...[
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: onAction,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _getButtonColor(type),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: Text(
                        _getActionText(type),
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _hasSiteInfo(final NotificationModel? notification) {
    // return notification?.siteName != null || notification?.siteAddress != null;
    return true;
  }

  bool _hasActionButton(final NotificationDisplayType? type) {
    return type == NotificationDisplayType.checkInReminder ||
        type == NotificationDisplayType.checkOutReminder;
  }

  String _getActionText(final NotificationDisplayType type) {
    switch (type) {
      case NotificationDisplayType.checkInReminder:
        return 'Check In';
      case NotificationDisplayType.checkOutReminder:
        return 'Check Out';
      // ignore: no_default_cases
      default:
        return '';
    }
  }

  Color _getButtonColor(final NotificationDisplayType? type) {
    switch (type) {
      case NotificationDisplayType.checkInReminder:
        return Colors.blue;
      case NotificationDisplayType.checkOutReminder:
        return Colors.green;
      // ignore: no_default_cases
      default:
        return Colors.grey;
    }
  }
}
