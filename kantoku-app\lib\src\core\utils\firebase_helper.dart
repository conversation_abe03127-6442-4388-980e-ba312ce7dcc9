// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:ez_firebase/ez_firebase.dart';
import 'package:in_app_notification/in_app_notification.dart';

// Project imports:
import '../../data/models/notification_model.dart';
import '../../domain/usecases/firebase_token_save_usecase.dart';
import '../../injector/injector.dart';
import '../../presentation/widgets/in_app_notification_widget.dart';
import '../enums/notification_type.dart';
import '../logger/logger.dart';
import '../routes/app_router.dart';
import '../routes/routes.dart';
import 'deeplink_helper.dart';

// // Package imports:
// import 'package:ez_firebase/ez_firebase.dart';

class FirebaseHelper {
  // this method will run when app is foreground
  // and run after user tap notification on background

  static Future<void> handleNotification(
    final BuildContext context,
    final RemoteMessage message, {
    required final bool isBackground,
  }) async {
    try {
      final notification = NotificationModel.fromJson(message.data);

      if (isBackground) {
        Future<void>.delayed(const Duration(seconds: 1)).then((final _) {
          if (context.mounted) {
            handleNotificationType(context, notification);
          }
        });
      } else {
        _showInAppMessage(message, notification);
      }
    } catch (e) {
      Log.error(e.toString());
    }
  }

  static Future<void> _showInAppMessage(
    final RemoteMessage message,
    final NotificationModel? notification,
  ) async {
    final context = getIt<AppRouter>().navigatorKey.currentContext;
    if (context != null) {
      InAppNotification.show(
        child: InAppNotificationWidget(
          notification: notification,
          message: message,
          onAction: () => _handleNotificationAction(context, notification),
        ),
        context: context,
        duration: const Duration(seconds: 5),
      );
    }
  }

  static Future<void> handleNotificationType(
    final BuildContext context,
    final NotificationModel? notification,
  ) async {
    try {
      final type = NotificationType.values
              .firstWhereOrNull((final e) => e.name == notification?.type) ??
          NotificationType.none;

      switch (type) {
        case NotificationType.text:
        case NotificationType.href:
          final href = notification?.href ?? '';
          if (href.isNotEmpty) {
            getIt<DeeplinkHelper>().handleDeepLinkRoutes(
              Uri.parse(href),
            );
          }
          break;
        case NotificationType.none:
        case NotificationType.message:
        case NotificationType.screen:
        case NotificationType.function:
          break;
      }

      // Handle specific notification display types for routing
      await _handleSpecificNotificationRouting(context, notification);
    } catch (_) {}
  }

  static Future<void> _handleNotificationAction(
    final BuildContext context,
    final NotificationModel? notification,
  ) async {
    final notificationType = notification?.type;

    switch (notificationType) {
      case 'checkInReminder':
      case 'checkOutReminder':
        // Navigate to log hours screen for check-in/check-out actions
        getIt<DeeplinkHelper>().handleDeepLinkRoutes(
          Uri.parse(Routes.logHours),
        );
        break;
      default:
        // For other notification types, handle via href if available
        final href = notification?.href ?? '';
        if (href.isNotEmpty) {
          getIt<DeeplinkHelper>().handleDeepLinkRoutes(
            Uri.parse(href),
          );
        }
        break;
    }
  }

  static Future<void> _handleSpecificNotificationRouting(
    final BuildContext context,
    final NotificationModel? notification,
  ) async {
    final notificationType = notification?.type;

    switch (notificationType) {
      case 'checkInReminder':
      case 'checkOutReminder':
        // Route to log hours screen
        getIt<DeeplinkHelper>().handleDeepLinkRoutes(
          Uri.parse(Routes.logHours),
        );
        break;
      case 'requestCreated':
      case 'requestApproved':
        // Route to day-off request screen
        getIt<DeeplinkHelper>().handleDeepLinkRoutes(
          Uri.parse(Routes.dayoffRequest),
        );
        break;
      default:
        // Handle other types via href if available
        final href = notification?.href ?? '';
        if (href.isNotEmpty) {
          getIt<DeeplinkHelper>().handleDeepLinkRoutes(
            Uri.parse(href),
          );
        }
        break;
    }
  }

  static Future<void> saveFirebaseToken(final String? token) async {
    final saveFirebaseTokenUseCase = getIt<FirebaseTokenSaveUseCase>();
    await saveFirebaseTokenUseCase(params: token);
  }
}
